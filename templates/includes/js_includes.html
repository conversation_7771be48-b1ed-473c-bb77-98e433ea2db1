{% load static %}

<!-- 基础JavaScript库 -->
<script src="{% static 'libraries_v4/assets/js/jquery-3.6.0.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v4/assets/js/bootstrap.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v4/assets/js/swiper-3.4.2.jquery.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v4/assets/js/popper.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v4/assets/js/jquery.scrollAnimations.min.js' %}" type="text/javascript" charset="utf-8"></script>

<!-- 功能性JavaScript -->
<script src="{% static 'libraries/mdi/js/dropzone.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v2/scripts/global.onlinechat.min.js' %}" type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v2/PlugIns/jquery.cookie.js' %}" type="text/javascript" charset="utf-8"></script>

<!-- 页面特定JavaScript -->
<script src="{% static 'libraries_v4/view/product/product.js' %}"></script>
<script src="{% static 'libraries_v4/view/home/<USER>' %}" defer></script>
<script src="{% static 'libraries_v4/layout/header/index.js' %}" defer type="text/javascript" charset="utf-8"></script>
<script src="{% static 'libraries_v4/layout/footer/index.js' %}" defer></script>

<!-- 页面初始化脚本 -->
<script type="text/javascript">
    (function ($) {
        // 动画圆圈效果（已注释）
        // setInterval(function () {
        //   if ($(".animated-circles").hasClass("animated")) {
        //     $(".animated-circles").removeClass("animated");
        //   } else {
        //     $(".animated-circles").addClass("animated");
        //   }
        // }, 3000);

        // 聊天提示效果（已注释）
        // var wait = setInterval(function () {
        //   $(".livechat-hint").removeClass("show_hint").addClass("hide_hint");
        //   clearInterval(wait);
        // }, 4500);
        // $(".livechat-girl")
        //   .hover(
        //     function () {
        //       clearInterval(wait);
        //       $(".livechat-hint")
        //         .removeClass("hide_hint")
        //         .addClass("show_hint");
        //     },
        //     function () {
        //       $(".livechat-hint")
        //         .removeClass("show_hint")
        //         .addClass("hide_hint");
        //     }
        //   )
    })(jQuery)
</script>
