{% extends 'includes/base.html' %}

{% load static %}

{% block title %}安全动态 - 思而听网络科技有限公司{% endblock %}

{% block content %}
    <!-- 页面头部横幅 -->
    <section class="position-relative bg-cover bg-center bg-no-repeat py-5 opacity-90"
             style="margin-top: 60px; background-image: url('{% static 'images/bgddc.webp' %}'),linear-gradient(180deg,#daebff,#f9fcff); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <div class="container position-relative" style="z-index: 10;">
            <div class="text-center">
                <h1 class="display-4 fw-bold text-white mb-3">安全动态</h1>
                <p class="fs-5 text-muted">关注网络安全最新动态，洞察行业发展趋势</p>
            </div>
        </div>
    </section>

    <!-- 面包屑导航 -->
    <div class="container">
        <nav aria-label="breadcrumb" class="py-4 border-bottom border-light mb-4">
            <ol class="breadcrumb mb-0 small text-muted">
                <li class="breadcrumb-item">
                    <a href="/" class="text-muted text-decoration-none">首页</a>
                </li>
                <li class="breadcrumb-item text-dark" aria-current="page">安全动态</li>
                {% if active_category %}
                    <li class="breadcrumb-item text-dark" aria-current="page">{{ active_category_name }}</li>
                {% endif %}
            </ol>
        </nav>
    </div>

    <!-- 主要内容区域 -->
    <div class="container py-4">
        <div class="row g-4">
            <!-- 左侧文章列表 -->
            <div class="col-lg-8">
                <!-- 搜索栏 -->
                <div class="flex items-center bg-white rounded-lg shadow-sm p-4 mb-6">
                    <img class="w-6 h-6 mr-3" src="{% static 'libraries_v4/assets/imgs/search.png' %}" alt="搜索">
                    <form method="get" class="flex items-center w-full">
                        <input type="text" name="q" placeholder="搜索文章..."
                               value="{{ request.GET.q }}"
                               class="flex-1 border-0 bg-transparent outline-none text-gray-700">
                        <button type="submit" class="text-blue-600 hover:text-blue-800 font-medium">
                            搜索
                        </button>
                    </form>
                </div>

                <!-- 文章列表头部 -->
                <div class="flex justify-between items-center py-8 border-b border-gray-200 mb-8">
                    <div class="flex items-center">
                        <h4 class="text-xl font-bold text-gray-900 m-0">{{ active_category_name }}</h4>
                        <span class="ml-3 text-gray-600 text-sm">共 {{ paginator.count }} 篇文章</span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-gray-600 text-sm mr-2">排序：</span>
                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}order=latest"
                           class="text-blue-600 hover:text-blue-800 text-sm mr-3">最新发布</a>
                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}order=popular"
                           class="text-blue-600 hover:text-blue-800 text-sm">最多浏览</a>
                    </div>
                </div>

                <!-- 文章列表 -->
                {% if articles %}
                    {% for article in articles %}
                        <div class="bg-white rounded-lg shadow-sm mb-6 overflow-hidden hover:shadow-md transition-shadow">
                        <div class="{% if article.image %}grid grid-cols-1 md:grid-cols-3{% else %}block{% endif %}">
                            {% if article.image %}
                                <div class="md:col-span-1">
                                    <img src="{{ article.image.url }}"
                                         alt="{{ article.title }}"
                                         class="w-full h-full object-cover"
                                         style="min-height: 150px;">
                                </div>
                                <div class="md:col-span-2">
                            {% else %}
                                <div>
                            {% endif %}
                            <div class="p-6">
                                <div class="flex items-center mb-3">
                                    <span class="bg-blue-600 text-white text-xs px-2 py-1 rounded mr-2">{{ article.category.name }}</span>
                                    <span class="text-gray-500 text-xs">{{ article.created_at|date:"Y-m-d H:i" }}</span>
                                    <span class="text-gray-500 text-xs ml-3">浏览 {{ article.views }}</span>
                                </div>
                                <h5 class="text-lg font-semibold mb-3">
                                    <a href="{% url 'news:article_detail' article.pk %}"
                                       class="text-gray-900 hover:text-blue-600 transition-colors">{{ article.title }}</a>
                                </h5>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ article.description }}</p>
                                <div class="flex items-center justify-between">
                                    <div class="text-gray-500 text-xs">
                                        关键词：{{ article.keywords|truncatechars:50 }}
                                    </div>
                                    <a href="{% url 'news:article_detail' article.pk %}"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">阅读全文 →</a>
                                </div>
                            </div>
                            </div>
                            </div>
                        </div>
                    {% endfor %}

                    <!-- 分页导航 -->
                    {% if is_paginated %}
                        <nav aria-label="文章分页" class="mt-8">
                            <div class="flex justify-center">
                                <div class="flex space-x-1">
                                    {% if page_obj.has_previous %}
                                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page=1"
                                           class="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50">首页</a>
                                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.previous_page_number }}"
                                           class="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50">上一页</a>
                                    {% endif %}

                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <span class="px-3 py-2 text-sm text-white bg-blue-600 border border-blue-600 rounded-md">{{ num }}</span>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ num }}"
                                               class="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ num }}</a>
                                        {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.next_page_number }}"
                                           class="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50">下一页</a>
                                        <a href="?{% if request.GET.q %}q={{ request.GET.q }}&{% endif %}page={{ page_obj.paginator.num_pages }}"
                                           class="px-3 py-2 text-sm text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50">末页</a>
                                    {% endif %}
                                </div>
                            </div>
                        </nav>
                    {% endif %}
                {% else %}
                    <!-- 无文章时的提示 -->
                    <div class="text-center py-12">
                        <img src="{% static 'libraries_v4/assets/imgs/no-data.png' %}"
                             alt="暂无数据" class="mx-auto mb-4" style="width: 120px;">
                        <h5 class="text-lg font-medium text-gray-500 mb-2">暂无相关文章</h5>
                        <p class="text-gray-500 text-sm mb-4">
                            {% if request.GET.q %}
                                没有找到包含"{{ request.GET.q }}"的文章，请尝试其他关键词
                            {% else %}
                                该分类下暂无文章，敬请期待
                            {% endif %}
                        </p>
                        <a href="/news"
                           class="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">查看全部文章</a>
                    </div>
                {% endif %}
                </div>

                <!-- 右侧边栏 -->
                <div class="lg:col-span-4">
                    <div class="lg:ml-8">
                        <!-- 文章分类 -->
                        <div class="bg-gray-100 p-6 rounded-lg mb-6">
                            <h5 class="text-lg font-bold text-gray-900 mb-4">文章分类</h5>
                            <div class="space-y-2">
                                <a href="/news"
                                   class="block px-3 py-2 rounded-md text-sm {% if not active_category %}bg-blue-600 text-white{% else %}text-gray-700 hover:bg-gray-200{% endif %} transition-colors">
                                    全部文章
                                </a>
                                {% for category in categories %}
                                    <a href="{% url 'news:category_article_list' category.id %}"
                                       class="block px-3 py-2 rounded-md text-sm {% if active_category == category.id %}bg-blue-600 text-white{% else %}text-gray-700 hover:bg-gray-200{% endif %} transition-colors">
                                        {{ category.name }}
                                    </a>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- 热门文章 -->
                        <div class="bg-gray-100 p-6 rounded-lg mb-6">
                            <h5 class="text-lg font-bold text-gray-900 mb-4">热门文章</h5>
                            <div class="space-y-4">
                                <div class="border-b border-gray-200 pb-3">
                                    <div class="flex">
                                        <div class="mr-3">
                                            <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-500 rounded">1</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="mb-1">
                                                <a href="/news/security-threats-2024"
                                                   class="text-gray-900 text-sm hover:text-blue-600 transition-colors">2024年网络安全威胁趋势分析与防护策略</a>
                                            </div>
                                            <div class="text-gray-500 text-xs">浏览量：1,234</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 pb-3">
                                    <div class="flex">
                                        <div class="mr-3">
                                            <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-yellow-500 rounded">2</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="mb-1">
                                                <a href="/news/ai-cybersecurity"
                                                   class="text-gray-900 text-sm hover:text-blue-600 transition-colors">AI赋能网络安全：思而听智能防护体系解析</a>
                                            </div>
                                            <div class="text-gray-500 text-xs">浏览量：987</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="border-b border-gray-200 pb-3">
                                    <div class="flex">
                                        <div class="mr-3">
                                            <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-green-500 rounded">3</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="mb-1">
                                                <a href="/news/enterprise-security"
                                                   class="text-gray-900 text-sm hover:text-blue-600 transition-colors">企业网络安全建设指南：从零基础到全面防护</a>
                                            </div>
                                            <div class="text-gray-500 text-xs">浏览量：756</div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div class="flex">
                                        <div class="mr-3">
                                            <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-500 rounded">4</span>
                                        </div>
                                        <div class="flex-1">
                                            <div class="mb-1">
                                                <a href="/news/emergency-response"
                                                   class="text-gray-900 text-sm hover:text-blue-600 transition-colors">网络安全应急响应实战：48小时内快速恢复业务</a>
                                            </div>
                                            <div class="text-gray-500 text-xs">浏览量：623</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 热门标签 -->
                        <div class="bg-gray-100 p-6 rounded-lg mb-6">
                            <h5 class="text-lg font-bold text-gray-900 mb-4">热门标签</h5>
                            <div class="flex flex-wrap gap-2">
                                <a href="/news?q=网络安全"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">网络安全</a>
                                <a href="/news?q=威胁情报"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">威胁情报</a>
                                <a href="/news?q=应急响应"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">应急响应</a>
                                <a href="/news?q=安全培训"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">安全培训</a>
                                <a href="/news?q=攻防演练"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">攻防演练</a>
                                <a href="/news?q=安全运营"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">安全运营</a>
                                <a href="/news?q=CTF竞赛"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">CTF竞赛</a>
                                <a href="/news?q=人工智能"
                                   class="px-3 py-1 text-xs border border-blue-600 text-blue-600 rounded-full hover:bg-blue-600 hover:text-white transition-colors">人工智能</a>
                            </div>
                        </div>

                        <!-- 联系我们 -->
                        <div class="bg-blue-600 text-white p-6 rounded-lg">
                            <h5 class="text-lg font-bold mb-3">需要专业安全服务？</h5>
                            <p class="text-sm mb-4">思而听为您提供全方位的网络安全解决方案</p>
                            <div class="mb-4 space-y-1">
                                <div class="text-sm">📞 咨询热线：400-123-4567</div>
                                <div class="text-sm">📧 邮箱：<EMAIL></div>
                                <div class="text-sm">🌐 官网：www.sierting.com</div>
                            </div>
                            <a href="/contact"
                               class="inline-block px-4 py-2 bg-white text-blue-600 rounded-md hover:bg-gray-100 transition-colors text-sm font-medium">立即咨询</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}